import React from 'react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-xl font-bold mb-4">CodeeTechs Ads</h3>
            <p className="text-gray-400">
              Your trusted partner for digital advertising success. 
              We help businesses grow through strategic online marketing.
            </p>
          </div>
          <div>
            <h4 className="text-lg font-semibold mb-4">Services</h4>
            <ul className="space-y-2 text-gray-400">
              <li><a href="#" className="hover:text-white transition-colors">Search Engine Marketing</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Social Media Ads</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Display Advertising</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Video Marketing</a></li>
            </ul>
          </div>
          <div>
            <h4 className="text-lg font-semibold mb-4">Company</h4>
            <ul className="space-y-2 text-gray-400">
              <li><a href="#" className="hover:text-white transition-colors">About Us</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Our Team</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Careers</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
            </ul>
          </div>
          <div>
            <h4 className="text-lg font-semibold mb-4">Contact Info</h4>
            <div className="space-y-2 text-gray-400">
              <p>📧 <EMAIL></p>
              <p>📞 +1 (555) 123-4567</p>
              <p>📍 123 Business Ave, Suite 100</p>
              <p>   New York, NY 10001</p>
            </div>
          </div>
        </div>
        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2024 CodeeTechs Ads. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
