import React from 'react';

const Services: React.FC = () => {
  const services = [
    {
      title: "Search Engine Marketing",
      description: "Dominate search results with targeted Google Ads and SEO strategies that drive qualified traffic to your business.",
      icon: "🔍"
    },
    {
      title: "Social Media Advertising",
      description: "Engage your audience across Facebook, Instagram, LinkedIn, and Twitter with compelling ad campaigns.",
      icon: "📱"
    },
    {
      title: "Display Advertising",
      description: "Capture attention with visually stunning banner ads and retargeting campaigns across premium websites.",
      icon: "🎨"
    },
    {
      title: "Video Marketing",
      description: "Tell your story with powerful video ads on YouTube, social platforms, and connected TV.",
      icon: "🎥"
    },
    {
      title: "Analytics & Reporting",
      description: "Track performance with detailed analytics and insights to optimize your advertising ROI.",
      icon: "📊"
    },
    {
      title: "Brand Strategy",
      description: "Develop a comprehensive brand strategy that resonates with your target audience and drives results.",
      icon: "🎯"
    }
  ];

  return (
    <section id="services" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Our Services</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Comprehensive digital advertising solutions tailored to your business needs
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div key={index} className="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow">
              <div className="text-4xl mb-4">{service.icon}</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">{service.title}</h3>
              <p className="text-gray-600">{service.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;
